use std::fs;
use std::path::PathBuf;
use tracing_appender::non_blocking::WorkerGuard;
use tracing_appender::rolling::{RollingFileAppender, Rotation};
use tracing_subscriber::fmt::writer::MakeWriterExt;
use tracing_subscriber::layer::SubscriberExt;
use tracing_subscriber::util::SubscriberInitExt;
use tracing_subscriber::{self, fmt};

pub fn init(app_data_dir: PathBuf) -> (WorkerGuard, WorkerGuard) {
    let log_level = std::env::var("RUST_LOG").unwrap_or_else(|_| "info".to_string());

    let log_dir = app_data_dir.join("db").join("logs");

    fs::create_dir_all(&log_dir).expect("无法创建日志目录");
    // Configure file logging with custom file name format
    let file_appender = RollingFileAppender::builder()
        .rotation(Rotation::DAILY)
        .filename_prefix("psmodel")
        .filename_suffix("log")
        .build(log_dir)
        .expect("Failed to initialize rolling file appender");

    let (non_blocking_file, guard_file) = tracing_appender::non_blocking(file_appender);
    let file_layer = fmt::layer()
        .with_writer(non_blocking_file.with_max_level(tracing::Level::INFO))
        .json();

    // Configure console logging
    let (non_blocking_stdout, guard_stdout) = tracing_appender::non_blocking(std::io::stdout());
    let stdout_layer =
        fmt::layer().with_writer(non_blocking_stdout.with_max_level(tracing::Level::DEBUG));

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| log_level.into()),
        )
        .with(file_layer)
        .with(stdout_layer)
        .init();

    (guard_file, guard_stdout)
}
