use lmdb::{Database, DatabaseFlags, Environment};
use std::path::PathBuf;

// 初始化 LMDB 环境
pub fn setup_lmdb_environment(app_data_dir: PathBuf) -> lmdb::Result<(Environment, Database)> {
    let db_path = app_data_dir.join("db");

    let env = Environment::new()
        .set_max_dbs(1) // 支持最多10个数据库
        .set_map_size(100 * 1024 * 1024) // 100MB
        .open(&db_path)
        .expect("打开数据目录失败");
    // 创建/打开单个数据库
    let db = env
        .create_db(Some("model"), DatabaseFlags::empty())
        .expect("创建数据库失败");
    Ok((env, db))
}
