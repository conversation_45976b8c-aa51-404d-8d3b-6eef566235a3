{"name": "psmodel", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "start": "npm run tauri dev", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "serve:web": "cargo run --bin web --manifest-path src-tauri/Cargo.toml", "dev:web": "concurrently \"npm run dev\" \"npm run serve:web\"", "rebuild": "cd src-tauri && cargo clean && cd .. && rm -rf dist/ && npm run build && npm run tauri build -- --no-bundle", "rebuild:run": "npm run rebuild && ./src-tauri/target/release/psmodel"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-export": "^2.1.6", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-plugin-scroller": "^2.0.10", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.2", "@element-plus/icons-vue": "^2.3.1", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "^2.3.1", "@tauri-apps/plugin-fs": "^2.4.1", "@tauri-apps/plugin-http": "^2.5.0", "@tauri-apps/plugin-opener": "^2", "@types/lodash-es": "^4.17.12", "axios": "^1.10.0", "axios-tauri-api-adapter": "^2.0.6", "echarts": "^5.5.1", "element-plus": "^2.8.2", "lodash-es": "^4.17.21", "pinia": "^2.2.2", "svgson": "^5.3.1", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/node": "^22.5.4", "@vitejs/plugin-vue": "^5.2.1", "concurrently": "^9.2.0", "sass": "^1.78.0", "sass-loader": "^16.0.1", "typescript": "~5.6.2", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}