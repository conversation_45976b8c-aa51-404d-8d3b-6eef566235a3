<template>
  <div class="component-editor">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <img src="@/assets/images/icon/slice_50.png" alt="logo" class="menu-back" @click="goBackMaterial"/>
        <h2>元件编辑器</h2>
        <span class="subtitle">基础元件设计工具</span>
      </div>
      <div class="toolbar-right">
        <el-button @click="openPinSettings()" class="settings-btn">
          <el-icon><Setting /></el-icon>
        </el-button>
        <el-button @click="clearCanvas">清空画布</el-button>
        <el-button @click="exportCanvas">导出</el-button>
         <el-button @click="handleUpload">导入</el-button>
        <el-button type="primary" @click="saveComponent">保存元件</el-button>
      </div>
    </div>

    <div class="editor-container">
      <!-- 左侧元件面板 -->
      <div class="elements-panel">
        <div class="panel-header"> 
          <h3>基础元件</h3>
        </div>       
        <div class="elements-grid">
          <div
            v-for="element in basicElements"
            :key="element.key"
            class="element-item"
            :data-type="element.key"
            @mousedown="startDrag"
          >
            <div
              class="element-icon"
              :class="{ 'pin-icon': element.key === 'basic-pin' }"
            >
              {{ element.icon }}
            </div>
            <span class="element-name">{{ element.name }}</span>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-container" ref="canvasContainerRef">
        <!-- 新增的绘图工具栏 -->
        <div class="drawing-toolbar" v-if="false">
          <div class="toolbar-group">
            <el-button><img src="@/assets/images/icon/slice_38.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_37.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_4.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_36.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_35.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_34.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_33.png" /></el-button>
          </div>
          <div class="toolbar-divider"></div>
          <div class="toolbar-group">
            <el-button><img src="@/assets/images/icon/slice_32.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_31.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_30.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_29.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_28.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_26.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_25.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_24.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_23.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_22.png" /></el-button>
          </div>
          <div class="toolbar-divider"></div>
          <div class="toolbar-group">
            <el-button><img src="@/assets/images/icon/slice_21.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_20.png" /></el-button>
            <el-button><img src="@/assets/images/icon/slice_16.png" /></el-button>
          </div>
          <div class="toolbar-divider"></div>
          <div class="toolbar-group">
            <el-button><img src="@/assets/images/icon/slice_3.png" /></el-button>
          </div>
        </div>
        <div id="editor-canvas"></div>

        

        <PinSettingsPanel
          v-model:visible="showPinSettings"
          v-model:settings="pinSettings"
          :inputs="componentDefinition.inputs"
          :outputs="componentDefinition.outputs"
        />


      </div>

      <!-- 右侧属性面板 -->
      <div class="properties-panel">
        <div class="panel-header">
          <el-tabs v-model="activeTab" class="panel-tabs">
            <el-tab-pane label="属性" name="properties"></el-tab-pane>
            <el-tab-pane label="定义" name="definition"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="properties-content">
          <!-- 属性面板 -->
          <div v-if="activeTab === 'properties'">
            <div v-if="selectedNode" class="property-form">
              <div class="property-group">
                <label>X 坐标</label>
                <el-input-number
                  v-model="nodeX"
                  :precision="0"
                  :step="1"
                  @change="updateNodePosition"
                />
              </div>
              <div class="property-group">
                <label>Y 坐标</label>
                <el-input-number
                  v-model="nodeY"
                  :precision="0"
                  :step="1"
                  @change="updateNodePosition"
                />
              </div>
              
              <div class="property-group">
                <label>宽度</label>
                <el-input-number
                  v-model="nodeWidth"
                  :min="1"
                  :max="500"
                  :step="1"
                  @change="updateNodeSize"
                />
              </div>
              <div class="property-group">
                <label>高度</label>
                <el-input-number
                  v-model="nodeHeight"
                  :min="1"
                  :max="500"
                  :step="1"
                  @change="updateNodeSize"
                />
              </div>
              <div
                class="property-group"
                v-if="selectedNode.shape === 'basic-text'"
              >
                <label>文本</label>
                <el-input
                  v-model="nodeText"
                  @input="updateNodeText"
                  placeholder="输入文本"
                />
              </div>
              <div class="property-group">
                <label>颜色</label>
                <el-color-picker v-model="nodeColor" @change="updateNodeColor" />
              </div>
              <div
                class="property-group"
                v-if="selectedNode.shape !== 'basic-pin' && selectedNode.name !== 'line'"
              >
                <label>线条宽度</label>
                <el-input-number
                  v-model="nodeStrokeWidth"
                  :min="1"
                  :max="10"
                  :step="1"
                  @change="updateNodeStroke"
                />
              </div>
              <div
                class="property-group"
                v-if="selectedNode.shape !== 'basic-pin' && selectedNode.name !== 'line'"
              >
                <label>线条颜色</label>
                <el-color-picker
                  v-model="nodeStrokeColor"
                  @change="updateNodeStroke"
                />
              </div>
              <div
                class="property-group"
                v-if="selectedNode.shape !== 'basic-pin' && selectedNode.name !== 'line'"
              >
                <label>填充颜色</label>
                <el-color-picker
                  v-model="nodeFillColor"
                  show-alpha
                  @change="updateNodeFill"
                />
              </div>
            </div>
            <div v-else class="no-selection">
              <p>请选择一个元件</p>
            </div>
          </div>
          
          <!-- 定义面板 -->
          <div v-if="activeTab === 'definition'" class="definition-form">
            <div class="definition-basic-info">
              <div class="form-row">
                <label>元件名称</label>
                <el-input
                  v-model="componentDefinition.name"
                 
                  readonly
                />
              </div>
              
              <div class="form-row">
                <label>元件关键字</label>
                <el-input
                  v-model="componentDefinition.keyword"
                  
                  readonly
                />
              </div>
              
              <div class="form-row">
                <label>元件备注</label>
                <el-input
                  v-model="componentDefinition.description"
                  readonly
                  type="textarea"
                  :rows="3"
                  resize="none"
                />
              </div>
            </div>
            
            <div class="scrollable-list">
              <div class="definition-params-section">
                <div class="section-header">
                  <span>参数</span>
                  <div class="header-actions">
                    <el-button link type="primary" @click="showConditionSettings = true">条件</el-button>
                    <el-button size="small" type="primary" @click="addParameter">
                      <el-icon><Plus /></el-icon>
                    </el-button>
                  </div>
                </div>
                
                <div v-for="(param, index) in componentDefinition.parameters" :key="index" class="parameter-item">
                  <div class="param-header">
                    <span class="param-title">参数{{ index + 1 }}</span>
                    <div class="param-actions">
                      <el-button size="small" type="danger" @click="removeParameter(index)">
                        <el-icon><Close /></el-icon>
                      </el-button>
                    </div>
                  </div> 
                  
                  <div class="param-content">
                    <div class="form-row">
                      <label>参数标签名</label>
                      <el-input v-model="param.label" placeholder="请输入" />
                    </div>
                    <div class="form-row">
                      <label>单位</label>
                      <el-input v-model="param.unit" placeholder="请输入" />
                    </div>
                    <div class="form-row">
                      <label>参数约束</label>
                      <div class="constraint-inputs">
                        <el-input-number v-model="param.constraint_min" :controls="false" placeholder="最小值" />
                        <span class="separator">-</span>
                        <el-input-number v-model="param.constraint_max" :controls="false" placeholder="最大值" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="definition-io-section">
                <div class="section-header">
                  <span>输入/输出配置</span>
                </div>
                
                <div class="io-subsection">
                  <div class="subsection-header">
                    <span>输入配置</span>
                    <el-button size="small" type="primary" @click="addInput">
                      <el-icon><Plus /></el-icon>
                    </el-button>
                  </div>
                  
                  <div v-for="(input, index) in componentDefinition.inputs" :key="input.id" class="io-item">
                    <div class="io-header">
                      <div class="io-content">
                        <div class="form-row">
                          <el-input v-model="input.name" placeholder="请输入"/>
                        </div>
                      </div>
                      <div class="io-actions">
                        <el-button size="small" type="danger" @click="removeInput(index)">
                          <el-icon><Close /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="io-subsection">
                  <div class="subsection-header">
                    <span>输出配置</span>
                    <el-button size="small" type="primary" @click="addOutput">
                      <el-icon><Plus /></el-icon>
                    </el-button>
                  </div>
                  
                  <div v-for="(output, index) in componentDefinition.outputs" :key="output.id" class="io-item">
                    <div class="io-header">
                      <div class="io-content">
                        <div class="form-row">
                          <el-input v-model="output.name" placeholder="请输入" />
                        </div>
                      </div>
                      <div class="io-actions">
                        <el-button size="small" type="danger" @click="removeOutput(index)">
                          <el-icon><Close /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
<ConditionSettingsDialog v-model:visible="showConditionSettings" :parameters="componentDefinition.parameters" :conditions="componentDefinition.conditions" @confirm="handleConditionConfirm" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from "vue";
import { useRoute, useRouter } from 'vue-router';
// import { useComponentStore } from '@/store/modules/component'; // 不再需要本地存储
import { Graph } from "@antv/x6";
import { Dnd } from "@antv/x6-plugin-dnd";
import { Transform } from "@antv/x6-plugin-transform";
import { Selection } from "@antv/x6-plugin-selection";
import { Snapline } from "@antv/x6-plugin-snapline";
import { Scroller } from '@antv/x6-plugin-scroller';
import { Export } from "@antv/x6-plugin-export";
import { ElMessage, ElMessageBox } from "element-plus";
import { open } from '@tauri-apps/plugin-dialog';
import { readTextFile } from '@tauri-apps/plugin-fs';
import { Plus, Close, Setting } from "@element-plus/icons-vue";
import ConditionSettingsDialog from './components/ConditionSettingsDialog.vue';
import PinSettingsPanel, { type PinSettings } from './components/PinSettingsPanel.vue';
import { registerAllBasicElements, basicElementsList } from "./basicElements";
import { PinSnapManager } from "./pinSnapManager";
import { getComponent, saveDiagram, getDiagram, updateComponent } from '@/api';
import { v4 as uuidv4 } from 'uuid';
import { parse as parseSvg } from 'svgson';

// 注册基本元件
registerAllBasicElements();

const showConditionSettings = ref(false);

const route = useRoute();
const router = useRouter();
// const componentStore = useComponentStore(); // 不再需要本地存储
const componentId = ref<string | null>(route.params.id as string || null);

// 添加component_type_id变量
const currentComponentTypeId = ref<number | null>(null);

// 实现goBackMaterial方法
const goBackMaterial = () => {
  if (currentComponentTypeId.value) {
    // 跳转到Material页面并传递component_type_id参数
    router.push({
      path: '/material',
      query: {
        component_type_id: currentComponentTypeId.value
      }
    });
  } else {
    // 如果没有component_type_id，直接跳转到Material页面
    router.push('/material');
  }
};

// 响应式数据
const selectedNode = ref<any>(null);
const nodeX = ref(0);
const nodeY = ref(0);
const nodeWidth = ref(0);
const nodeHeight = ref(0);
const nodeText = ref("");
const nodeColor = ref("#000000");
const nodeStrokeWidth = ref(2);
const nodeStrokeColor = ref("#000000");
const nodeFillColor = ref("rgba(255,255,255,0)");
const canvasContainerRef = ref<HTMLElement | null>(null);

// Tab切换
const activeTab = ref("properties");

// 引脚设置面板
const showPinSettings = ref(false);
const pinSettings = ref<PinSettings>({
  name: "",
  visible: true,
  dataType: "real",
  pinType: 'electrical',
  signal: "",
  description: ""
});
const pinSettingsMap = ref<Map<string, PinSettings>>(new Map());

interface ComponentDefinition {
  name: string;
  keyword: string;
  description: string;
  parameters: Array<{
    required: boolean;
    label: string;
    unit: string;
    constraint_min: number | undefined;
    constraint_max: number | undefined;
  }>;
  inputs: Array<{
    id: string;
    name: string;
  }>;
  outputs: Array<{
    id: string;
    name: string;
  }>;
  pinSettings?: Array<PinSettings & { id:string }>;
  conditions?: any; // 添加conditions属性
  importedSvgs?: Array<{
    id: string;
    shape: string;
    svgContent: string;
    position: { x: number; y: number };
    size: { width: number; height: number };
  }>; // 添加导入的SVG信息
}

const logConditions = () => {
  console.log('Current conditions:', componentDefinition.value.conditions);
};

// 处理条件设置确认
const handleConditionConfirm = (conditions: any) => {
  componentDefinition.value.conditions = conditions;
  console.log('Updated conditions:', componentDefinition.value.conditions);
};

// 组件定义数据
const componentDefinition = ref<ComponentDefinition>({
  name: "",
  keyword: "",
  description: "",
  parameters: [],
  inputs: [],
  outputs: [],
  conditions: { displayConditions: [], usageConditions: [] } // 初始化conditions
});

// 基础元件列表
const basicElements = basicElementsList;

// X6 实例
let graph: Graph;
let dnd: Dnd;
let pinSnapManager: PinSnapManager;

// 键盘事件处理函数引用
let keyboardHandler: (e: KeyboardEvent) => void;

// 初始化画布
const initCanvas = () => {
  graph = new Graph({
    container: document.getElementById("editor-canvas")!,
    // width: 1000,  // 固定宽度，专业标准尺寸
    // height: 700,  // 固定高度，适合元件设计
    background: {
      color: "#fafafa",
    },
    grid: {
      visible: true,
      type: "dot",
      args: {
        color: "#e0e0e0",
        thickness: 1,
      },
      size: 2,
    },
    panning: {
      enabled: true,
      eventTypes: ["leftMouseDown", "mouseWheel"],
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: "ctrl",
      minScale: 0.5,
      maxScale: 3,
    },
    // 禁用连接功能，专注于元件编辑
    connecting: {
      allowBlank: false,
      allowLoop: false,
      allowNode: false,
      allowEdge: false,
      allowPort: false, // 禁用所有连接
    },
  });

  graph.use(
    new Selection({
      enabled: true,
      rubberband: true,
      showNodeSelectionBox: true,
      modifiers: 'ctrl',
    }),
  );

  graph.use(new Scroller());

  // 添加插件
  graph.use(
    new Transform({
      resizing: {
        enabled: true,
        minWidth: 5, // 最小宽度5px
        minHeight: 5, // 最小高度5px
        preserveAspectRatio: false,
        orthogonal: false,
      },
      rotating: {
        enabled: true,
      },
    })
  );

  graph.use(
    new Selection({
      enabled: true,
      multiple: true,
      rubberband: true,
      movable: true,
      // 限制选择区域在画布内
      strict: true,
    })
  );

  graph.use(
    new Snapline({
      enabled: true,
      sharp: true,
      tolerance: 1,
    })
  );

  graph.use(new Export());

  // 初始化拖拽
  dnd = new Dnd({
    target: graph,
    validateNode() {
      return true;
    },
  });

  // 初始化引脚自动吸附管理器
  //pinSnapManager = new PinSnapManager(graph);

  // 事件监听
  setupEventListeners();
};

// 设置事件监听
const setupEventListeners = () => {
  // 节点选择
  graph.on("node:click", ({ node }) => {
    selectNode(node);
    // 如果选中的是引脚，则加载其设置
    if (node.shape === 'basic-pin') {
      const settings = pinSettingsMap.value.get(node.id);
      if (settings) {
        pinSettings.value = { ...settings };
      } else {
        // 如果Map中没有，则创建一个新的默认设置
        const defaultSettings: PinSettings = {
          name: `pin_${node.id}`,
          visible: true,
          dataType: "real",
          pinType: 'electrical',
          signal: "",
          description: ""
        };
        pinSettingsMap.value.set(node.id, defaultSettings);
        pinSettings.value = { ...defaultSettings };
      }
    }
  });

  graph.on('node:dblclick', ({ node }) => {
    if (node.shape === 'basic-text') {
      const pos = node.position();
      const size = node.size();
      const foreignObject = document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject');
      const body = document.createElement('body');
      const input = document.createElement('div');

      // 设置初始宽度，但允许动态调整
      let currentWidth = Math.max(size.width, 100); // 最小宽度100px
      
      foreignObject.setAttribute('x', pos.x.toString());
      foreignObject.setAttribute('y', pos.y.toString());
      foreignObject.setAttribute('width', currentWidth.toString());
      foreignObject.setAttribute('height', size.height.toString());

      body.setAttribute('xmlns', 'http://www.w3.org/1999/xhtml');
      body.style.margin = '0';
      body.style.padding = '0';
      body.style.backgroundColor = 'transparent';
      body.style.width = '100%';
      body.style.height = '100%';

      input.contentEditable = 'true';
      input.style.width = '100%';
      input.style.height = '100%';
      input.style.border = '1px solid #1890ff';
      input.style.backgroundColor = '#fff';
      input.style.outline = 'none';
      input.style.boxSizing = 'border-box';
      input.style.whiteSpace = 'pre-wrap'; // 允许换行
      input.style.wordWrap = 'break-word'; // 自动换行
      input.style.overflow = 'hidden'; // 隐藏溢出
      input.innerText = node.getAttrByPath('text/text') || '';

      body.appendChild(input);
      foreignObject.appendChild(body);

      const svg = graph.view.svg;
      svg.appendChild(foreignObject);

      input.focus();

      // 动态调整宽度函数
      const adjustWidth = () => {
        const text = input.innerText || '';
        if (text.trim() === '') {
          // 如果文本为空，使用最小宽度
          currentWidth = 100;
        } else {
          // 创建临时元素测量文本宽度
          const tempDiv = document.createElement('div');
          tempDiv.style.position = 'absolute';
          tempDiv.style.visibility = 'hidden';
          tempDiv.style.whiteSpace = 'pre-wrap';
          tempDiv.style.wordWrap = 'break-word';
          tempDiv.style.font = window.getComputedStyle(input).font;
          tempDiv.style.width = 'auto';
          tempDiv.style.height = 'auto';
          tempDiv.innerText = text;
          document.body.appendChild(tempDiv);
          
          const textWidth = tempDiv.offsetWidth;
          const textHeight = tempDiv.offsetHeight;
          document.body.removeChild(tempDiv);
          
          // 计算新宽度，考虑padding和border
          const padding = 8; // 假设左右padding各4px
          const border = 2; // 左右border各1px
          const minWidth = 100;
          const maxWidth = 800; // 最大宽度限制
          
          currentWidth = Math.max(minWidth, Math.min(maxWidth, textWidth + padding + border));
          
          // 如果文本高度超过容器高度，需要调整高度
          if (textHeight > size.height) {
            const newHeight = Math.max(size.height, textHeight + 10);
            foreignObject.setAttribute('height', newHeight.toString());
            node.resize(size.width, newHeight);
          }
        }
        
        // 更新foreignObject宽度
        foreignObject.setAttribute('width', currentWidth.toString());
        
        // 更新节点宽度
        node.resize(currentWidth, node.getSize().height);
      };

      // 监听输入事件，动态调整宽度
      input.addEventListener('input', adjustWidth);
      input.addEventListener('keydown', adjustWidth);

      const handleBlur = () => {
        const finalText = input.innerText;
        node.setAttrByPath('text/text', finalText);
        
        // 最终调整一次宽度
        adjustWidth();
        
        svg.removeChild(foreignObject);
        input.removeEventListener('blur', handleBlur);
        input.removeEventListener('input', adjustWidth);
        input.removeEventListener('keydown', adjustWidth);
      };

      input.addEventListener('blur', handleBlur);
      
      // 初始调整一次宽度
      adjustWidth();
    }
  });

  // 节点添加
  graph.on('node:added', ({ node }) => {
    if (node.shape === 'basic-pin') {
      const defaultSettings: PinSettings = {
        name: `pin_${node.id}`,
        visible: true,
        dataType: 'real',
        pinType: 'electrical',
        signal: '',
        description: '',
      };
      pinSettingsMap.value.set(node.id, defaultSettings);
      // 选中新添加的引脚并加载其设置
      selectNode(node);
      pinSettings.value = { ...defaultSettings };
    }
  });

  // 点击空白区域取消选择
  graph.on("blank:click", () => {
    selectedNode.value = null;
  });

  // 节点删除
  graph.on("node:removed", ({ node }) => {
    if (pinSettingsMap.value.has(node.id)) {
      pinSettingsMap.value.delete(node.id);
    }
    if (selectedNode.value && selectedNode.value.id === node.id) {
      selectedNode.value = null;
    }
  });

  // 节点移动时实时更新坐标显示（不强制对齐）
  graph.on("node:moved", ({ node }) => {
    if (selectedNode.value && selectedNode.value.id === node.id) {
      const position = node.position();

      // 只更新显示，不强制对齐
      nodeX.value = Math.round(position.x);
      nodeY.value = Math.round(position.y);
    }
  });

  // 节点移动时实时更新坐标（无网格对齐，完全跟随鼠标）
  graph.on("node:moving", ({ node }) => {
    if (selectedNode.value && selectedNode.value.id === node.id) {
      const position = node.position();
      nodeX.value = Math.round(position.x);
      nodeY.value = Math.round(position.y);
    }
  });

  // 节点拖拽结束时可选择性进行网格对齐（当前禁用）
  graph.on("node:change:position", ({ node, options }) => {
    // 暂时禁用自动对齐，让移动完全跟随鼠标
    if (false && options.ui && !options.skipAlign) {
      const position = node.position();

      // 自动对齐到网格（当前禁用）
      const alignedX = Math.round(position.x / 10) * 10;
      const alignedY = Math.round(position.y / 10) * 10;

      if (
        Math.abs(position.x - alignedX) > 0.1 ||
        Math.abs(position.y - alignedY) > 0.1
      ) {
        node.position(alignedX, alignedY, { skipAlign: true });

        if (selectedNode.value && selectedNode.value.id === node.id) {
          nodeX.value = alignedX;
          nodeY.value = alignedY;
        }
      }
    }
  });

  // 键盘控制选中节点位置和删除
  keyboardHandler = (e: KeyboardEvent) => {
    // 检查当前焦点元素是否为输入框或可编辑元素
    const activeElement = document.activeElement as HTMLElement;
    const isInputFocused =
      activeElement &&
      (activeElement.tagName === "INPUT" ||
        activeElement.tagName === "TEXTAREA" ||
        activeElement.contentEditable === "true" ||
        activeElement.classList.contains("el-input__inner") ||
        activeElement.classList.contains("el-textarea__inner"));
  
    // 如果当前焦点在输入框中，不处理任何键盘事件（包括删除键和方向键）
    if (isInputFocused) {
      return;
    }
  
    if (e.key === "Delete" || e.key === "Backspace") {
      if (selectedNode.value) {
        e.preventDefault();
        graph.removeNode(selectedNode.value);
        selectedNode.value = null;
      }
    } else if (
      e.key === "ArrowUp" ||
      e.key === "ArrowDown" ||
      e.key === "ArrowLeft" ||
      e.key === "ArrowRight"
    ) {
      if (selectedNode.value) {
        e.preventDefault();
        const position = selectedNode.value.position();
        const step = 1;
        let newX = position.x;
        let newY = position.y;
  
        switch (e.key) {
          case "ArrowUp":
            newY -= step;
            break;
          case "ArrowDown":
            newY += step;
            break;
          case "ArrowLeft":
            newX -= step;
            break;
          case "ArrowRight":
            newX += step;
            break;
        }
  
        selectedNode.value.position(newX, newY, { skipAlign: true });
        
        // 同步更新显示的坐标值
        nodeX.value = newX;
        nodeY.value = newY;
      }
    }
  };

  document.addEventListener("keydown", keyboardHandler);

  // 节点缩放时实时更新尺寸
  graph.on("node:resized", ({ node }) => {
    if (selectedNode.value && selectedNode.value.id === node.id) {
      const size = node.getSize();
      nodeWidth.value = size.width;
      nodeHeight.value = size.height;
    }
  });

  // 智能边界显示
  graph.on("node:mouseenter", ({ node }) => {
    if (node.shape === "basic-pin") {
      // 引脚：显示虚线边框和十字光标
      node.setAttrByPath("body/opacity", 1); // 显示虚线边框
      node.setAttrByPath("body/cursor", "crosshair"); // 十字光标
      node.setAttrByPath("dot/r", 2.5); // 中心点微微放大
      node.setAttrByPath("dot/fill", "#1565c0"); // 中心点颜色稍微深一点
    } else {
      // 其他元件：显示边界辅助
      node.addTools([
        {
          name: "boundary",
          args: {
            attrs: {
              fill: "#1976d2",
              stroke: "#1976d2",
              strokeWidth: 1,
              fillOpacity: 0.05,
              strokeDasharray: "5,5",
            },
          },
        },
      ]);
    }
  });

  // 隐藏边界
  graph.on("node:mouseleave", ({ node }) => {
    if (node.shape === "basic-pin") {
      // 引脚：隐藏虚线边框，恢复中心点
      node.setAttrByPath("body/opacity", 0); // 隐藏虚线边框
      node.setAttrByPath("dot/r", 2); // 恢复中心点大小
      node.setAttrByPath("dot/fill", "#1976d2"); // 恢复中心点颜色
    } else {
      // 其他元件：移除边界辅助
      node.removeTools();
    }
  });
};


// 选择节点
const selectNode = (node: any) => {
  selectedNode.value = node;

  // 获取位置信息
  const position = node.position();
  nodeX.value = Math.round(position.x);
  nodeY.value = Math.round(position.y);

  // 获取尺寸信息
  const size = node.getSize();
  nodeWidth.value = size.width;
  nodeHeight.value = size.height;

  // 获取文本内容
  const text = node.getAttrByPath("text/text");
  nodeText.value = text || "";

  // 获取样式属性
  const stroke = node.getAttrByPath("body/stroke");
  nodeColor.value = stroke || "#000000";
  nodeStrokeColor.value = stroke || "#000000";

  const strokeWidth = node.getAttrByPath("body/strokeWidth");
  nodeStrokeWidth.value = strokeWidth || 2;

  const fill = node.getAttrByPath("body/fill");
  nodeFillColor.value = fill || "rgba(255,255,255,0)";
};

// 开始拖拽
const startDrag = (e: MouseEvent) => {
  const target = e.currentTarget as HTMLElement;
  const type = target.getAttribute("data-type");

  if (!type) return;

  const node = graph.createNode({
    shape: type,
  });

  dnd.start(node, e);
};

// 更新节点尺寸 - 自动对齐到5的倍数
// const updateNodeSize = () => {
//   if (selectedNode.value) {
//     // 自动对齐到5的倍数，最小值为5
//     const alignedWidth = Math.max(Math.round(nodeWidth.value / 5) * 5, 5);
//     const alignedHeight = Math.max(Math.round(nodeHeight.value / 5) * 5, 5);

//     // 更新节点真实尺寸
//     selectedNode.value.size(alignedWidth, alignedHeight);

//     // 更新显示值
//     nodeWidth.value = alignedWidth;
//     nodeHeight.value = alignedHeight;
//   }
// };
const updateNodeSize = () => {
  if (selectedNode.value) {
    // 更新节点真实尺寸
    selectedNode.value.size(nodeWidth.value, nodeHeight.value);
  }
};


// 更新节点文本
const updateNodeText = () => {
  if (selectedNode.value) {
    selectedNode.value.setAttrByPath("text/text", nodeText.value);
  }
};

// 更新节点颜色
const updateNodeColor = () => {
  if (selectedNode.value) {
    if (selectedNode.value.name === 'line') {
      selectedNode.value.setAttrByPath("line/stroke", nodeColor.value);
    } else {
      selectedNode.value.setAttrByPath("body/stroke", nodeColor.value);
      selectedNode.value.setAttrByPath("text/fill", nodeColor.value);
    }
  }
};

// 更新节点线条样式
const updateNodeStroke = () => {
  if (selectedNode.value) {
    selectedNode.value.setAttrByPath("body/stroke", nodeStrokeColor.value);
    selectedNode.value.setAttrByPath("body/strokeWidth", nodeStrokeWidth.value);
    selectedNode.value.setAttrByPath("line/stroke", nodeStrokeColor.value);
    selectedNode.value.setAttrByPath("line/strokeWidth", nodeStrokeWidth.value);
  }
};

// 更新节点填充
const updateNodeFill = () => {
  if (selectedNode.value) {
    selectedNode.value.setAttrByPath("body/fill", nodeFillColor.value);
  }
};



const openPinSettings = (node?: any) => {
  const targetNode = node || selectedNode.value;
  if (targetNode && targetNode.shape === 'basic-pin') {
    loadPinSettings(targetNode);
  } else {
    resetPinSettings();
  }
  showPinSettings.value = !showPinSettings.value;
};

const loadPinSettings = (node: any) => {
  const attrs = node.getAttrs();
  let pinType: 'electrical' | 'controlIn' | 'controlOut' = 'electrical';
  if (attrs.pinType?.includes('controlIn')) {
    pinType = 'controlIn';
  } else if (attrs.pinType?.includes('controlOut')) {
    pinType = 'controlOut';
  }

  pinSettings.value = {
    name: attrs.label?.text || "",
    visible: attrs.body?.visibility !== 'hidden',
    dataType: attrs.dataType === 'real' ? 'real' : '',
    pinType: pinType,
    signal: attrs.signal || "",
    description: attrs.description || ""
  };
};

const resetPinSettings = () => {
  pinSettings.value = {
    name: "",
    visible: true,
    dataType: "real",
    pinType: 'electrical',
    signal: "",
    description: ""
  };
};

watch(pinSettings, (newSettings) => {
  if (selectedNode.value && selectedNode.value.shape === 'basic-pin') {
    const nodeId = selectedNode.value.id;
    if (pinSettingsMap.value.has(nodeId)) {
      pinSettingsMap.value.set(nodeId, { ...newSettings });
      // 同时更新到X6节点上，以便实时预览
      selectedNode.value.setAttrs({
        label: {
          text: newSettings.name
        },
        body: {
          visibility: newSettings.visible ? 'visible' : 'hidden'
        },
        pinType: [newSettings.pinType],
        dataType: newSettings.dataType,
        signal: newSettings.signal,
        description: newSettings.description
      });
    }
  }
}, { deep: true });

// 更新节点位置 - 支持自由定位
const updateNodePosition = () => {
  if (selectedNode.value) {
    // 直接使用用户输入的坐标，不强制对齐
    selectedNode.value.position(nodeX.value, nodeY.value, { skipAlign: true });
  }
};

// 清空画布
const clearCanvas = () => {
  ElMessageBox.confirm("确定要清空画布吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    graph.clearCells();
    selectedNode.value = null;
    ElMessage.success("画布已清空");
  });
};

// 创建矩形节点
const createRectNode = (element: any, transform: any) => {
  const attrs = element.attributes || {};
  const x = parseFloat(attrs.x || '0') * transform.scaleX + transform.x;
  const y = parseFloat(attrs.y || '0') * transform.scaleY + transform.y;
  const width = parseFloat(attrs.width || '100') * transform.scaleX;
  const height = parseFloat(attrs.height || '100') * transform.scaleY;

  return {
    shape: 'basic-rect',
    x,
    y,
    width,
    height,
    attrs: {
      body: {
        fill: attrs.fill || 'transparent',
        stroke: attrs.stroke || '#000000',
        strokeWidth: parseFloat(attrs['stroke-width'] || '2'),
        rx: parseFloat(attrs.rx || '0'),
        ry: parseFloat(attrs.ry || '0'),
      }
    }
  };
};

// 创建圆形节点
const createCircleNode = (element: any, transform: any) => {
  const attrs = element.attributes || {};
  const cx = parseFloat(attrs.cx || '0') * transform.scaleX + transform.x;
  const cy = parseFloat(attrs.cy || '0') * transform.scaleY + transform.y;
  const r = parseFloat(attrs.r || '50') * Math.min(transform.scaleX, transform.scaleY);

  return {
    shape: 'basic-circle',
    x: cx - r,
    y: cy - r,
    width: r * 2,
    height: r * 2,
    attrs: {
      body: {
        fill: attrs.fill || 'transparent',
        stroke: attrs.stroke || '#000000',
        strokeWidth: parseFloat(attrs['stroke-width'] || '2'),
      }
    }
  };
};

// 创建椭圆节点
const createEllipseNode = (element: any, transform: any) => {
  const attrs = element.attributes || {};
  const cx = parseFloat(attrs.cx || '0') * transform.scaleX + transform.x;
  const cy = parseFloat(attrs.cy || '0') * transform.scaleY + transform.y;
  const rx = parseFloat(attrs.rx || '50') * transform.scaleX;
  const ry = parseFloat(attrs.ry || '30') * transform.scaleY;

  return {
    shape: 'basic-circle', // 使用圆形作为基础，通过宽高比实现椭圆
    x: cx - rx,
    y: cy - ry,
    width: rx * 2,
    height: ry * 2,
    attrs: {
      body: {
        fill: attrs.fill || 'transparent',
        stroke: attrs.stroke || '#000000',
        strokeWidth: parseFloat(attrs['stroke-width'] || '2'),
      }
    }
  };
};

// 创建线条节点
const createLineNode = (element: any, transform: any) => {
  const attrs = element.attributes || {};
  const x1 = parseFloat(attrs.x1 || '0') * transform.scaleX + transform.x;
  const y1 = parseFloat(attrs.y1 || '0') * transform.scaleY + transform.y;
  const x2 = parseFloat(attrs.x2 || '100') * transform.scaleX + transform.x;
  const y2 = parseFloat(attrs.y2 || '0') * transform.scaleY + transform.y;

  const width = Math.abs(x2 - x1);
  const height = Math.abs(y2 - y1);
  const minX = Math.min(x1, x2);
  const minY = Math.min(y1, y2);

  return {
    shape: 'basic-line',
    x: minX,
    y: minY,
    width: Math.max(width, 2), // 最小宽度2px
    height: Math.max(height, 2), // 最小高度2px
    attrs: {
      body: {
        fill: attrs.stroke || '#000000',
        strokeWidth: parseFloat(attrs['stroke-width'] || '2'),
      }
    }
  };
};

// 创建多边形节点
const createPolygonNode = (element: any, transform: any) => {
  const attrs = element.attributes || {};
  const points = attrs.points || '';

  // 解析points属性
  const pointsArray = points.split(/[\s,]+/).filter(Boolean);
  if (pointsArray.length < 6) return null; // 至少需要3个点（6个数字）

  // 计算边界框
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
  for (let i = 0; i < pointsArray.length; i += 2) {
    const x = parseFloat(pointsArray[i]) * transform.scaleX + transform.x;
    const y = parseFloat(pointsArray[i + 1]) * transform.scaleY + transform.y;
    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x);
    maxY = Math.max(maxY, y);
  }

  return {
    shape: 'basic-triangle', // 使用三角形作为基础多边形
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY,
    attrs: {
      body: {
        fill: attrs.fill || 'transparent',
        stroke: attrs.stroke || '#000000',
        strokeWidth: parseFloat(attrs['stroke-width'] || '2'),
      }
    }
  };
};

// 创建路径节点
const createPathNode = (element: any, transform: any) => {
  const attrs = element.attributes || {};

  // 对于复杂的path，我们创建一个矩形占位符
  // 在实际应用中，可能需要更复杂的路径解析
  return {
    shape: 'basic-rect',
    x: transform.x,
    y: transform.y,
    width: 100 * transform.scaleX,
    height: 100 * transform.scaleY,
    attrs: {
      body: {
        fill: attrs.fill || 'transparent',
        stroke: attrs.stroke || '#000000',
        strokeWidth: parseFloat(attrs['stroke-width'] || '2'),
      }
    }
  };
};

// 创建文本节点
const createTextNode = (element: any, transform: any) => {
  const attrs = element.attributes || {};
  const x = parseFloat(attrs.x || '0') * transform.scaleX + transform.x;
  const y = parseFloat(attrs.y || '0') * transform.scaleY + transform.y;
  const text = element.children?.[0]?.value || element.value || 'Text';

  return {
    shape: 'basic-text',
    x,
    y: y - 15, // 调整Y位置，因为SVG文本基线不同
    width: 80,
    height: 30,
    attrs: {
      text: {
        text,
        fontSize: parseFloat(attrs['font-size'] || '16'),
        fill: attrs.fill || '#000000',
        fontFamily: attrs['font-family'] || 'Arial',
      }
    }
  };
};

// SVG元素转换为X6节点的函数
const convertSvgElementToNode = (element: any, parentTransform = { x: 0, y: 0, scaleX: 1, scaleY: 1 }) => {
  const nodes: any[] = [];

  if (!element) return nodes;

  // 解析transform属性
  const transform = parseTransform(element.attributes?.transform || '');
  const currentTransform = {
    x: parentTransform.x + (transform.x || 0) * parentTransform.scaleX,
    y: parentTransform.y + (transform.y || 0) * parentTransform.scaleY,
    scaleX: parentTransform.scaleX * (transform.scaleX || 1),
    scaleY: parentTransform.scaleY * (transform.scaleY || 1)
  };

  switch (element.name) {
    case 'rect':
      const rect = createRectNode(element, currentTransform);
      if (rect) nodes.push(rect);
      break;

    case 'circle':
      const circle = createCircleNode(element, currentTransform);
      if (circle) nodes.push(circle);
      break;

    case 'ellipse':
      const ellipse = createEllipseNode(element, currentTransform);
      if (ellipse) nodes.push(ellipse);
      break;

    case 'line':
      const line = createLineNode(element, currentTransform);
      if (line) nodes.push(line);
      break;

    case 'polyline':
    case 'polygon':
      const poly = createPolygonNode(element, currentTransform);
      if (poly) nodes.push(poly);
      break;

    case 'path':
      const path = createPathNode(element, currentTransform);
      if (path) nodes.push(path);
      break;

    case 'text':
      const text = createTextNode(element, currentTransform);
      if (text) nodes.push(text);
      break;

    case 'g':
      // 处理组元素，递归处理子元素
      if (element.children) {
        element.children.forEach((child: any) => {
          nodes.push(...convertSvgElementToNode(child, currentTransform));
        });
      }
      break;
  }

  return nodes;
};

// 解析transform属性
const parseTransform = (transformStr: string) => {
  const result = { x: 0, y: 0, scaleX: 1, scaleY: 1 };

  if (!transformStr) return result;

  // 解析translate
  const translateMatch = transformStr.match(/translate\(([^)]+)\)/);
  if (translateMatch) {
    const values = translateMatch[1].split(/[,\s]+/).map(Number);
    result.x = values[0] || 0;
    result.y = values[1] || 0;
  }

  // 解析scale
  const scaleMatch = transformStr.match(/scale\(([^)]+)\)/);
  if (scaleMatch) {
    const values = scaleMatch[1].split(/[,\s]+/).map(Number);
    result.scaleX = values[0] || 1;
    result.scaleY = values[1] || values[0] || 1;
  }

  return result;
};

// 导入SVG文件
const importSvgFile = async (svgContent: string) => {
  try {
    // 首先尝试解析SVG获取基本信息
    const parsed = await parseSvg(svgContent);
    console.log('Parsed SVG:', parsed);

    // 尝试转换为基础元素
    const basicNodes = convertSvgElementToNode(parsed);

    if (basicNodes.length > 0) {
      // 询问用户选择导入方式
      const action = await ElMessageBox.confirm(
        `检测到 ${basicNodes.length} 个基础元素。请选择导入方式：`,
        '选择导入方式',
        {
          distinguishCancelAndClose: true,
          confirmButtonText: '转换为基础元素',
          cancelButtonText: '保持原始SVG',
          type: 'info'
        }
      ).then(() => 'basic').catch((action) => {
        if (action === 'cancel') return 'svg';
        throw action;
      });

      if (action === 'basic') {
        // 导入为基础元素
        basicNodes.forEach(node => {
          graph.addNode(node);
        });
        ElMessage.success(`成功导入 ${basicNodes.length} 个基础元素`);
      } else {
        // 导入为完整SVG
        await importAsSvgElement(svgContent, parsed);
      }
    } else {
      // 没有找到基础元素，直接导入为SVG
      await importAsSvgElement(svgContent, parsed);
    }
  } catch (error) {
    console.error('SVG导入失败:', error);
    ElMessage.error('SVG文件解析失败');
  }
};

// 将SVG作为完整元素导入
const importAsSvgElement = async (svgContent: string, parsed: any) => {
  try {
    // 获取SVG的尺寸信息
    const svgAttrs = parsed.attributes || {};
    let width = parseFloat(svgAttrs.width || '200');
    let height = parseFloat(svgAttrs.height || '200');

    // 如果没有明确的宽高，尝试从viewBox获取
    if (svgAttrs.viewBox) {
      const viewBox = svgAttrs.viewBox.split(/\s+/);
      if (viewBox.length >= 4) {
        width = parseFloat(viewBox[2]) || width;
        height = parseFloat(viewBox[3]) || height;
      }
    }

    // 生成唯一的形状ID
    const uniqueShapeId = `imported-svg-${Date.now()}`;

    // 注册自定义SVG形状
    Graph.registerNode(uniqueShapeId, {
      inherit: 'rect',
      width,
      height,
      markup: [
        {
          tagName: 'foreignObject',
          selector: 'fo',
          attrs: {
            width: '100%',
            height: '100%',
          },
        },
      ],
      attrs: {
        fo: {
          body: {
            xmlns: 'http://www.w3.org/1999/xhtml',
            style: {
              width: '100%',
              height: '100%',
              margin: 0,
              padding: 0,
              overflow: 'hidden',
            },
          },
        },
      },
    }, true);

    // 创建节点并添加到画布
    const svgNode = graph.addNode({
      shape: uniqueShapeId,
      x: 100,
      y: 100,
      width,
      height,
      data: {
        svgContent,
        originalSvg: true
      }
    });

    // 渲染SVG内容
    setTimeout(() => {
      const cell = graph.getCellById(svgNode.id);
      if (cell) {
        const foreignObject = cell.findView(graph)?.findOne('foreignObject');
        if (foreignObject) {
          // 清理SVG内容，移除可能的XML声明和DOCTYPE
          const cleanSvg = svgContent
            .replace(/<\?xml[^>]*\?>/g, '')
            .replace(/<!DOCTYPE[^>]*>/g, '')
            .trim();

          foreignObject.innerHTML = `
            <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
              ${cleanSvg}
            </div>
          `;

          // 调整内部SVG的尺寸以适应容器
          const svgElement = foreignObject.querySelector('svg');
          if (svgElement) {
            svgElement.style.width = '100%';
            svgElement.style.height = '100%';
            svgElement.style.maxWidth = '100%';
            svgElement.style.maxHeight = '100%';
          }
        }
      }
    }, 100);

    ElMessage.success('成功导入完整SVG元素');
  } catch (error) {
    console.error('SVG元素导入失败:', error);
    ElMessage.error('SVG元素导入失败');
  }
};

// 导入JSON文件
const importJsonFile = async (jsonContent: string) => {
  try {
    const data = JSON.parse(jsonContent);

    // 如果是X6的JSON格式
    if (data.cells) {
      graph.fromJSON(data);
      ElMessage.success('成功导入画布数据');
    } else {
      ElMessage.warning('不支持的JSON格式');
    }
  } catch (error) {
    console.error('JSON导入失败:', error);
    ElMessage.error('JSON文件解析失败');
  }
};

// 恢复导入的SVG形状注册
const restoreImportedSvgs = async (importedSvgs: any[]) => {
  console.log('开始恢复SVG形状，总数:', importedSvgs.length);

  for (const svgInfo of importedSvgs) {
    try {
      console.log(`正在注册SVG形状: ${svgInfo.shape}`, svgInfo);

      // 强制重新注册，不检查是否存在
      Graph.registerNode(svgInfo.shape, {
        inherit: 'rect',
        width: svgInfo.size?.width || 200,
        height: svgInfo.size?.height || 200,
        markup: [
          {
            tagName: 'foreignObject',
            selector: 'fo',
            attrs: {
              width: '100%',
              height: '100%',
            },
          },
        ],
        attrs: {
          fo: {
            body: {
              xmlns: 'http://www.w3.org/1999/xhtml',
              style: {
                width: '100%',
                height: '100%',
                margin: 0,
                padding: 0,
                overflow: 'hidden',
              },
            },
          },
        },
      }, true);

      // 验证注册是否成功
      try {
        const testNode = graph.createNode({
          shape: svgInfo.shape,
          x: 0,
          y: 0,
          width: 100,
          height: 100
        });
        console.log(`SVG形状 ${svgInfo.shape} 注册验证成功`);
        // 不添加到画布，只是测试
      } catch (testError) {
        console.error(`SVG形状 ${svgInfo.shape} 注册验证失败:`, testError);
        throw testError;
      }

    } catch (error) {
      console.error(`恢复SVG形状 ${svgInfo.shape} 失败:`, error);
      // 注册失败时，尝试注册一个简单的矩形作为占位符
      try {
        Graph.registerNode(svgInfo.shape, {
          inherit: 'rect',
          width: svgInfo.size?.width || 200,
          height: svgInfo.size?.height || 200,
          attrs: {
            body: {
              fill: '#f0f0f0',
              stroke: '#ccc',
              strokeWidth: 2,
            },
            text: {
              text: 'SVG',
              fontSize: 14,
              fill: '#666',
            }
          }
        }, true);
        console.log(`为 ${svgInfo.shape} 注册了占位符形状`);
      } catch (fallbackError) {
        console.error(`为 ${svgInfo.shape} 注册占位符也失败:`, fallbackError);
      }
    }
  }

  console.log('所有SVG形状注册完成');
};

// 恢复SVG内容的渲染
const restoreSvgRendering = (importedSvgs: any[]) => {
  importedSvgs.forEach((svgInfo: any) => {
    try {
      const cell = graph.getCellById(svgInfo.id);
      if (cell) {
        const foreignObject = cell.findView(graph)?.findOne('foreignObject');
        if (foreignObject && svgInfo.svgContent) {
          // 清理SVG内容
          const cleanSvg = svgInfo.svgContent
            .replace(/<\?xml[^>]*\?>/g, '')
            .replace(/<!DOCTYPE[^>]*>/g, '')
            .trim();

          foreignObject.innerHTML = `
            <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
              ${cleanSvg}
            </div>
          `;

          // 调整内部SVG的尺寸
          const svgElement = foreignObject.querySelector('svg');
          if (svgElement) {
            svgElement.style.width = '100%';
            svgElement.style.height = '100%';
            svgElement.style.maxWidth = '100%';
            svgElement.style.maxHeight = '100%';
          }
        }
      }
    } catch (error) {
      console.warn(`恢复SVG渲染 ${svgInfo.id} 失败:`, error);
    }
  });
};

// 上传元件处理函数
const handleUpload = async () => {
  try {
    const selected = await open({
      multiple: false,
      filters: [{
        name: 'SVG Files',
        extensions: ['svg']
      }, {
        name: 'JSON Files',
        extensions: ['json']
      }]
    });

    if (typeof selected === 'string') {
      console.log('Selected file:', selected);
      const contents = await readTextFile(selected);

      // 判断文件类型
      if (selected.toLowerCase().endsWith('.svg')) {
        await importSvgFile(contents);
      } else if (selected.toLowerCase().endsWith('.json')) {
        await importJsonFile(contents);
      }
    } else {
      console.log('No file selected');
    }
  } catch (err) {
    console.error('Error selecting file:', err);
    ElMessage.error('文件选择失败');
  }
};

// 导出画布
const exportCanvas = () => {
  //不用ts校验 
  (graph as any).toPNG((dataUri: string) => {
    const link = document.createElement("a");
    link.download = "component.png";
    link.href = dataUri;
    link.click();
    ElMessage.success("导出成功");
  });
};

// 保存元件
const saveComponent = async () => {
  if (!componentDefinition.value.name) {
    ElMessage.error("请输入元件名称");
    return;
  }

  if (!componentId.value) {
    ElMessage.error("组件ID不存在，无法保存");
    return;
  }

  try {
    const canvasData = graph.toJSON();
    const pinSettingsArray = Array.from(pinSettingsMap.value.entries()).map(([id, settings]) => ({ id, ...settings }));

    // 收集导入的SVG信息
    const importedSvgs: any[] = [];
    const cells = canvasData.cells || [];

    cells.forEach((cell: any) => {
      if (cell.data?.originalSvg && cell.data?.svgContent) {
        importedSvgs.push({
          id: cell.id,
          shape: cell.shape,
          svgContent: cell.data.svgContent,
          position: { x: cell.position?.x || 0, y: cell.position?.y || 0 },
          size: { width: cell.size?.width || 200, height: cell.size?.height || 200 }
        });
      }
    });

    console.log('保存时收集到的SVG信息:', importedSvgs);

    const definitionWithPins = {
      ...componentDefinition.value,
      pinSettings: pinSettingsArray,
      importedSvgs, // 保存导入的SVG信息
    };

    // 调用API保存图数据
    const response = await saveDiagram({
      component_id: componentId.value,
      property: {}, // 可以根据需要添加属性数据
      definition: definitionWithPins,
      diagram: canvasData,
    });

    if (response.code === 200) {
      ElMessage.success(`元件 "${componentDefinition.value.name}" 保存成功`);
      router.push('/material');
    } else {
      ElMessage.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('保存组件失败:', error);
    ElMessage.error('保存组件失败');
  }
};



// 添加参数
const addParameter = () => {
  componentDefinition.value.parameters.push({
    required: false,
    label: "",
    unit: "",
    constraint_min: undefined,
    constraint_max: undefined,
  });
};

const removeParameter = (index: number) => {
  componentDefinition.value.parameters.splice(index, 1);
};

const addInput = () => {
  componentDefinition.value.inputs.push({
    id: uuidv4(),
    name: ""
  });
};

const removeInput = (index: number) => {
  componentDefinition.value.inputs.splice(index, 1);
};

const addOutput = () => {
  componentDefinition.value.outputs.push({
    id: uuidv4(),
    name: ""
  });
};

const removeOutput = (index: number) => {
  componentDefinition.value.outputs.splice(index, 1);
};

// 加载元件数据
const loadComponentData = async (id: string) => {
  try {
    const response = await getComponent({ component_id: id });
    if (response.code === 200 && response.data) {
      const componentData = response.data;
      
      // 存储component_type_id
      currentComponentTypeId.value = componentData.component_type_id;
      
      // 设置基本信息
      componentDefinition.value.name = componentData.name;
      componentDefinition.value.keyword = componentData.keyword;
      componentDefinition.value.description = componentData.description;
      
      // 根据is_diagram判断是否存在图数据
      if (componentData.is_diagram === 1) {
        // 存在图数据，调用API获取图数据
        try {
          const diagramResponse = await getDiagram({ component_id: id });

          console.log('图数据', diagramResponse);


          if (diagramResponse.code === 200 && diagramResponse.data) {
            const diagramData = diagramResponse.data;
            
            // 加载定义数据（如果存在）
            if (diagramData.definition) {
              console.log('原始definition数据:', diagramData.definition);
              const definition = diagramData.definition as ComponentDefinition & { importedSvgs?: any[] };
              const { pinSettings, importedSvgs, ...restDefinition } = definition;

              console.log('解析后的importedSvgs:', importedSvgs);
              console.log('解析后的pinSettings:', pinSettings);

              componentDefinition.value = {
                ...componentDefinition.value,
                ...restDefinition
              };

              // 加载引脚设置
              if (pinSettings && Array.isArray(pinSettings)) {
                pinSettingsMap.value = new Map(pinSettings.map(p => [p.id, p]));
              }

              // 重新注册导入的SVG形状 - 必须在fromJSON之前完成
              if (importedSvgs && Array.isArray(importedSvgs) && importedSvgs.length > 0) {
                console.log('正在恢复导入的SVG形状:', importedSvgs);
                await restoreImportedSvgs(importedSvgs);
              } else {
                console.log('没有找到importedSvgs数据或数据为空');
              }
            } else {
              console.log('没有definition数据');
            }

            // 加载图形数据 - 在形状注册完成后
            if (diagramData.diagram) {
              try {
                console.log('准备加载图形数据:', diagramData.diagram);

                // 检查diagram中的cells，看是否有未注册的形状
                if (diagramData.diagram.cells) {
                  const missingShapes: string[] = [];
                  const svgCells: any[] = [];

                  diagramData.diagram.cells.forEach((cell: any) => {
                    console.log('检查cell:', cell.id, cell.shape, cell.data);

                    if (cell.shape && cell.shape.startsWith('imported-svg-')) {
                      svgCells.push(cell);

                      // 尝试创建测试节点来验证形状是否可用
                      try {
                        const testNode = graph.createNode({
                          shape: cell.shape,
                          x: 0,
                          y: 0,
                          width: 100,
                          height: 100
                        });
                        console.log(`形状 ${cell.shape} 验证成功`);
                      } catch (e) {
                        console.error(`形状 ${cell.shape} 验证失败:`, e);
                        missingShapes.push(cell.shape);
                      }
                    }
                  });

                  console.log('找到的SVG cells:', svgCells);

                  if (missingShapes.length > 0) {
                    console.warn('发现未注册的形状:', missingShapes);
                    ElMessage.warning(`发现 ${missingShapes.length} 个未注册的SVG形状，可能导致显示异常`);

                    // 尝试为缺失的形状注册占位符
                    for (const missingShape of missingShapes) {
                      try {
                        Graph.registerNode(missingShape, {
                          inherit: 'rect',
                          width: 200,
                          height: 200,
                          attrs: {
                            body: {
                              fill: '#f0f0f0',
                              stroke: '#ff0000',
                              strokeWidth: 2,
                              strokeDasharray: '5,5',
                            },
                            text: {
                              text: 'Missing SVG',
                              fontSize: 12,
                              fill: '#ff0000',
                            }
                          }
                        }, true);
                        console.log(`为缺失形状 ${missingShape} 注册了占位符`);
                      } catch (e) {
                        console.error(`为缺失形状 ${missingShape} 注册占位符失败:`, e);
                      }
                    }
                  }
                }

                graph.fromJSON(diagramData.diagram);
                console.log('图形数据加载成功');

                // 恢复SVG内容的渲染
                const definitionData = diagramData.definition as any;
                if (definitionData?.importedSvgs) {
                  setTimeout(() => {
                    restoreSvgRendering(definitionData.importedSvgs);
                  }, 300); // 增加延迟确保DOM完全渲染
                }
              } catch (error) {
                console.error('图形数据加载失败:', error);

                // 尝试备用恢复方案：只加载基础元素
                try {
                  const filteredDiagram = {
                    ...diagramData.diagram,
                    cells: diagramData.diagram.cells?.filter((cell: any) =>
                      !cell.shape || !cell.shape.startsWith('imported-svg-')
                    ) || []
                  };

                  if (filteredDiagram.cells.length > 0) {
                    graph.fromJSON(filteredDiagram);
                    ElMessage.warning('部分图形数据加载成功，导入的SVG元素可能丢失');
                  } else {
                    ElMessage.warning('图形数据加载失败，画布为空');
                  }
                } catch (fallbackError) {
                  console.error('备用加载方案也失败:', fallbackError);
                  ElMessage.error('图形数据完全无法加载');
                }
              }
            }
          }
        } catch (diagramError) {
          console.error('加载图数据失败:', diagramError);
          ElMessage.warning('图数据加载失败，但组件基本信息已加载');
        }
      } else if (componentData.is_diagram === 2) {
        // 处理上传的SVG内容 - 动态注册方案
        try {
          const diagramResponse = await getDiagram({ component_id: id });
          
          if (diagramResponse.code === 200 && diagramResponse.data) {
            const diagramData = diagramResponse.data;
            
            if (diagramData.diagram && diagramData.diagram.svgContent) {
              const svgContent = diagramData.diagram.svgContent;
              const uniqueShapeId = `uploaded-svg-${id}`;
              
              // 动态注册SVG组件
              const { registerDynamicSvg } = await import('@/views/Customization/register');
              const registrationResult = registerDynamicSvg(uniqueShapeId, svgContent, {
                width: 300,
                height: 200,
                name: componentDefinition.value.name || '上传的SVG'
              });
              
              if (registrationResult && !registrationResult.error) {
                // 创建节点
                const svgNode = graph.addNode({
                  shape: uniqueShapeId,
                  x: 100,
                  y: 100,
                  width: 300,
                  height: 200,
                  data: {
                    isUploadedSvg: true,
                    originalSvgContent: svgContent
                  }
                });
                
                console.log('已注册并加载上传的SVG组件，引脚数量:', registrationResult.ports?.length || 0);
              } else {
                console.error('SVG组件注册失败:', registrationResult?.error);
                ElMessage.warning('SVG组件注册失败，但组件基本信息已加载');
              }
            }
            
            // 加载定义数据（如果存在）
            if (diagramData.definition) {
              const definition = diagramData.definition as ComponentDefinition;
              const { pinSettings, ...restDefinition } = definition;
              componentDefinition.value = {
                ...componentDefinition.value,
                ...restDefinition
              };
              
              // 加载引脚设置
              if (pinSettings && Array.isArray(pinSettings)) {
                pinSettingsMap.value = new Map(pinSettings.map(p => [p.id, p]));
              }
            }
          }
        } catch (diagramError) {
          console.error('加载SVG数据失败:', diagramError);
          ElMessage.warning('SVG数据加载失败，但组件基本信息已加载');
        }
      }
    } else {
      ElMessage.error(response.message || '获取元件数据失败');
      router.push('/component-editor');
    }
  } catch (error) {
    console.error('加载组件数据失败:', error);
    ElMessage.error('加载组件数据失败');
    router.push('/component-editor');
  }
};

// 组件挂载
onMounted(async () => {
  initCanvas();

  if (componentId.value) {
    await loadComponentData(componentId.value);
  }

  if (canvasContainerRef.value) {
    const resizeObserver = new ResizeObserver(() => {
      if (graph && canvasContainerRef.value) {
        const { clientWidth, clientHeight } = canvasContainerRef.value;
        graph.resize(clientWidth, clientHeight);
      }
    });
    resizeObserver.observe(canvasContainerRef.value);

    onUnmounted(() => {
      resizeObserver.disconnect();
    });
  }
});

// 组件卸载
onUnmounted(() => {
  // 清理资源
  if (pinSnapManager) {
    pinSnapManager.destroy();
  }
  if (graph) {
    graph.dispose();
  }
  // 移除键盘事件监听器
  if (keyboardHandler) {
    document.removeEventListener("keydown", keyboardHandler);
  }
});
</script>

<style scoped lang="scss">
.constraint-inputs {
  display: flex;
  align-items: center;
  width: 100%;

  .separator {
    margin: 0 6px;
    color: #909399;
    font-size: 12px;
  }

  .el-input-number {
    flex: 1;
    min-width: 65px;
    max-width: 75px;
  }
}

.component-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  overflow: hidden;
}

.drawing-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #fcfcfc;
  border-bottom: 1px solid #e0e0e0;
}

.toolbar-group {
  display: flex;
  align-items: center;
}

.toolbar-group .el-button {
  padding: 6px;
  margin: 0 2px;
  border: 1px solid transparent;
  background-color: transparent;
}

.toolbar-group .el-button:hover {
  border-color: #c0c0c0;
  background-color: #f0f0f0;
}

.toolbar-group .el-button img {
  height: 16px;
  width: 16px;
}

.toolbar-divider {
  height: 24px;
  width: 1px;
  background-color: #dcdfe6;
  margin: 0 8px;
}
.menu-back{
  width: 40px;
  height: 40px;
  filter: brightness(1.2) saturate(0.8)
}

.toolbar {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;

    h2 {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 500;
    }

    .subtitle {
      color: #666;
      font-size: 14px;
    }

    .canvas-info {
      color: #888;
      font-size: 12px;
      background: #f0f0f0;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: monospace;
    }
  }

  .toolbar-right {
    display: flex;
    gap: 12px;
  }
}

.editor-container {
  flex: 1;
  display: flex;
}

.elements-panel {
  width: 240px;
  background: #f8f9fa;
  border-right: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;

  .panel-header {
    border-bottom: 1px solid #e5e5e5;
    background: #fff;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .panel-tabs {
      :deep(.el-tabs__header) {
        margin: 0;
        padding: 0;
        background: #fff;
      }

      :deep(.el-tabs__nav-wrap) {
        padding: 0;
        
        &::after {
          height: 1px;
          background-color: #e4e7ed;
        }
      }

      :deep(.el-tabs__nav-scroll) {
        padding: 0;
        width: 100%;
      }

      :deep(.el-tabs__nav) {
        border: none;
        display: flex;
        width: 100%;
        justify-content: space-around;
      }

      :deep(.el-tabs__item) {
        height: 40px;
        line-height: 40px;
        padding: 0;
        font-size: 14px;
        color: #909399;
        border: none;
        font-weight: normal;
        text-align: center;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;

        &:hover {
          color: #303133;
        }

        &.is-active {
          color: #409eff;
          font-weight: 500;
        }
      }

      :deep(.el-tabs__active-bar) {
        background-color: #409eff;
        height: 2px;
        bottom: 0;
      }
      
      :deep(.el-tabs__content) {
        padding: 0;
      }
    }

    h3 {
      margin: 0;
      color: #333;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .elements-grid {
    flex: 1;
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    grid-auto-rows: 80px;
    align-content: start;
    overflow-y: auto;
  }

  .element-item {
    height: 80px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;

    &:hover {
      border-color: #1976d2;
      background: #f5f5f5;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .element-icon {
      font-size: 18px;
      margin-bottom: 6px;
      color: #1976d2;

      &.pin-icon {
        font-size: 12px;
      }
    }

    .element-name {
      font-size: 12px;
      color: #333;
      font-weight: 400;
    }
  }
}

.canvas-container {
  flex: 1;
  min-width: 0;
  position: relative;
  background: #fff;
  overflow: auto;

  #editor-canvas {
    width: 100%;
    height: 100%;
  }

  // 引脚设置触发按钮
  .pin-settings-trigger {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 40px;
    height: 40px;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    z-index: 100;

    &:hover {
      background: #f5f7fa;
      border-color: #409eff;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .el-icon {
      font-size: 18px;
      color: #606266;
    }
  }

  // 悬浮设置面板
  .floating-settings-panel {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 320px;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    z-index: 1001;
    display: flex;
    flex-direction: column;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

    .panel-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      border-bottom: 1px solid #e4e7ed;
      font-weight: 600;
      color: #303133;
      font-size: 16px;

      .close-btn {
        font-size: 16px;
        color: #909399;
        &:hover {
          color: #f56c6c;
        }
      }
    }

    .panel-content {
      padding: 16px;
      max-height: 450px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .setting-item {
        display: grid;
        grid-template-columns: 80px 1fr;
        align-items: center;
        gap: 12px;

        label {
          font-size: 13px;
          color: #606266;
          font-weight: 500;
        }

        .toggle-group {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #909399;
        }

        .checkbox-group {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }
      }
    }

    .panel-footer {
      display: flex;
      justify-content: flex-end;
      padding: 12px 16px;
      border-top: 1px solid #e4e7ed;
      background: #f5f7fa;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
      gap: 12px;
    }
  }

  // 引脚设置面板
  .pin-settings-panel {
    position: absolute;
    top: 16px;
    right: 70px;
    width: 280px;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    z-index: 200;
    overflow: hidden;

    .pin-panel-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      font-weight: 500;
      color: #303133;
      font-size: 14px;

      .close-btn {
        padding: 0;
        width: 20px;
        height: 20px;
        min-height: 20px;
        font-size: 14px;
        color: #909399;

        &:hover {
          color: #f56c6c;
        }
      }
    }

    .pin-panel-content {
      padding: 16px;
      max-height: 400px;
      overflow-y: auto;

      .pin-form-group {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          display: block;
          margin-bottom: 6px;
          color: #606266;
          font-size: 13px;
          font-weight: 500;
        }

        .pin-toggle {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #909399;
        }

        .pin-type-options {
          display: flex;
          flex-direction: column;
          gap: 6px;

          .el-checkbox {
            margin: 0;
            
            :deep(.el-checkbox__label) {
              font-size: 12px;
              color: #606266;
            }
          }
        }

        .el-checkbox {
          :deep(.el-checkbox__label) {
            font-size: 12px;
            color: #606266;
          }
        }

        .el-input,
        .el-select {
          :deep(.el-input__wrapper) {
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            
            &:hover {
              border-color: #409eff;
            }
            
            &.is-focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }
          }
        }
      }
    }
  }
}

.properties-panel {
  width: 320px;
  background: #f5f7fa;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;

  .panel-header {
    border-bottom: 1px solid #e4e7ed;
    background: #fff;
    
    .panel-tabs {
      :deep(.el-tabs__header) {
        margin: 0;
        padding: 0;
        background: #fff;
      }

      :deep(.el-tabs__nav-wrap) {
        padding: 0;
        width: 100%;
        
        &::after {
          height: 1px;
          background-color: #e4e7ed;
        }
      }

      :deep(.el-tabs__nav-scroll) {
        padding: 0;
        width: 100%;
      }

      :deep(.el-tabs__nav) {
        border: none;
        display: flex;
        width: 100%;
        justify-content: space-around;
      }

      :deep(.el-tabs__item) {
        height: 40px;
        line-height: 40px;
        padding: 0;
        font-size: 14px;
        color: #909399;
        border: none;
        font-weight: normal;
        text-align: center;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;

        &:hover {
          color: #303133;
        }

        &.is-active {
          color: #409eff;
          font-weight: 500;
        }
      }

      :deep(.el-tabs__active-bar) {
        background-color: #409eff;
        height: 2px;
        bottom: 0;
      }
      
      :deep(.el-tabs__content) {
        padding: 0;
      }
    }
  }

  .properties-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background: #f5f7fa;
    margin: 0;
    border-radius: 0;
    border: none;
  }

  .properties-form {
    padding: 16px;
  }

  .property-card {
    margin-bottom: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #ffffff;
  }

  .property-card:last-child {
    margin-bottom: 0;
  }

  .property-card .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0;
  }

  .property-card .card-title {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .property-card .form-content {
    padding: 0;
  }

  .property-card .el-form-item {
    margin-bottom: 16px;
  }

  .property-card .el-form-item:last-child {
    margin-bottom: 0;
  }

  .property-card .el-form-item__label {
    font-size: 12px;
    color: #606266;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;
  }

  .empty-state p {
    margin: 0;
    font-size: 14px;
  }

  .property-form {
    .property-group {
      margin-bottom: 16px;

      label {
        display: block;
        margin-bottom: 6px;
        color: #333;
        font-size: 14px;
        font-weight: 400;
      }

      :deep(.el-input-number),
      :deep(.el-input),
      :deep(.el-color-picker) {
        width: 100%;

        .el-input__wrapper {
          border: 1px solid #ddd;
          border-radius: 4px;
          background: #fff;

          &:hover {
            border-color: #1976d2;
          }

          &.is-focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
          }
        }
      }
    }
  }

  .no-selection {
    text-align: center;
    color: #999;
    margin-top: 40px;
    font-size: 14px;
  }

  .definition-form {
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
    
    .definition-basic-info {
      margin-bottom: 16px;
      
      .form-row {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        label {
          width: 90px;
          color: #606266;
          font-size: 13px;
          flex-shrink: 0;
          text-align: left;
        }
        
        .el-input {
          flex: 1;
          max-width: 180px;
          
          :deep(.el-input__wrapper) {
            height: 32px;
          }
        }
      }
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 16px 0 10px 0;
      padding-bottom: 6px;
      border-bottom: 1px solid #e5e5e5;
      font-weight: 500;
      color: #303133;
      font-size: 13px;

      span {
        flex: 1;
      }
      
      .el-button {
        padding: 5px 8px;
        height: 24px;
      }
    }

    .definition-params-section {
      margin-bottom: 20px;
      
      .parameter-item {
        background: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 10px 12px;
        margin-bottom: 10px;
        
        .param-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          
          .param-title {
            font-weight: normal;
            color: #303133;
            font-size: 13px;
          }
          
          .param-actions {
            display: flex;
            align-items: center;
            gap: 6px;
            
            .el-checkbox {
              margin-right: 4px;
            }
            
            .el-button {
              padding: 4px;
              height: 22px;
              width: 22px;
            }
          }
        }
        
        .param-content {
          .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            label {
              width: 90px;
              color: #606266;
              font-size: 12px;
              flex-shrink: 0;
              text-align: left;
            }
            
            .el-input, .constraint-inputs {
              flex: 1;
              
              :deep(.el-input__wrapper) {
                height: 28px;
              }
            }
            
            .el-input {
              max-width: 180px;
            }
          }
        }
      }
    }
    
    .definition-io-section {
      .io-subsection {
        margin-bottom: 16px;
        
        .subsection-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 12px 0 8px 0;
          padding-bottom: 4px;
          border-bottom: 1px solid #ebeef5;
          font-size: 12px;
          color: #909399;
          
          span {
            flex: 1;
          }
          
          .el-button {
            padding: 5px 8px;
            height: 24px;
          }
        }
        
        .io-item {
          background: #f5f7fa;
         
          border-radius: 4px;
          padding: 4px 6px;
          margin-bottom: 2px;
          
          .io-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            span {
              font-size: 12px;
              color: #303133;
            }
            
            .io-actions {
              display: flex;
              align-items: center;
              gap: 6px;
              
              .el-checkbox {
                margin-right: 4px;
              }
              
              .el-button {
                padding: 4px;
                height: 22px;
                width: 22px;
              }
            }
          }
          
          .io-content {
           
            
            .form-row {
              display: flex;
              align-items: center;
              
              label {
                width: 90px;
                color: #606266;
                font-size: 12px;
                flex-shrink: 0;
                text-align: left;
              }
              
              .el-input {
                flex: 1;
                max-width: 180px;
                
                :deep(.el-input__wrapper) {
                  height: 28px;
                }
              }
            }
          }
        }
      }
    }
  }
}

.canvas-container {
  position: relative;
}

.drawing-toolbar {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.drawing-toolbar .toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.drawing-toolbar .toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: #dcdfe6;
  margin: 0 8px;
}

.drawing-toolbar .el-button {
  padding: 6px;
  height: auto;
  min-width: auto;
  border: 1px solid transparent;
  background-color: transparent;
  transition: background-color 0.2s, border-color 0.2s;
}

.drawing-toolbar .el-button:hover {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
}

.drawing-toolbar .el-button img {
  height: 20px;
  width: 20px;
  vertical-align: middle;
}

.floating-settings-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 276px;
  background-color: #f0f2f5; // Updated background color
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  flex-direction: column;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    color: #303133;
    font-size: 14px;

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .divider {
        width: 1px;
        height: 16px;
        background-color: #dcdfe6;
      }

      .arrow-icon {
        cursor: pointer;
        color: #909399;
      }
    }
  }

  .panel-content {
    padding: 12px;
    background-color: #ffffff;
    border-top: 1px solid #dcdfe6;
    border-bottom: 1px solid #dcdfe6;

    .pin-settings-form {
      .el-form-item {
        margin-bottom: 12px;
      }

      .el-form-item__label {
        color: #606266;
        padding-right: 10px;
      }

      .switch-group {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #909399;
      }

      .checkbox-group {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .el-select {
        width: 100%;
      }
    }
  }

  .panel-footer {
    padding: 10px 12px;
    text-align: right;
    background-color: #f0f2f5;
  }
}

.scrollable-list {
  height: calc(100vh - 280px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
  padding-bottom: 20px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

</style>
